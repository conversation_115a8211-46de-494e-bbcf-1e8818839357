"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  Filter,
  Grid3X3,
  List,
  TrendingUp,
  TrendingDown,
  Sparkles,
  Brain,
  Dna,
  Zap,
  Cpu,
  DollarSign,
  Heart,
  Building,
  Lightbulb,
  Rocket,
  Globe,
  Atom,
  Microscope,
  Car,
  Home,
  ShoppingCart,
  Music,
  Palette,
  Camera,
  Gamepad2,
  Leaf,
  Shield,
  Waves,
  Calendar,
  Star,
  Eye,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  ExternalLink,
  ChevronDown,
  Users,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"

interface IdeaCollection {
  id: string
  name: string
  creator: string
  description: string
  floorPrice: number
  volume: number
  change: number
  itemCount: number
  image: string
  verified: boolean
}

interface TrendingIdea {
  id: string
  name: string
  symbol: string
  price: number
  change: number
  volume: number
  image: string
}

interface FeaturedDrop {
  id: string
  title: string
  creator: string
  description: string
  launchDate: string
  image: string
  category: string
}

interface TopMover {
  id: string
  title: string
  creator: string
  floorPrice: number
  change: number
  volume: number
  image: string
}

interface CreatorProfile {
  id: string
  name: string
  avatar: string
  totalIdeas: number
  floorPrice: number
  change: number
  verified: boolean
  rank: number
}

interface HeroIdea {
  id: string
  title: string
  creator: string
  description: string
  floorPrice: number
  totalVolume: number
  items: number
  listed: number
  change: number
  image: string
  icon: React.ComponentType<{ className?: string }>
  category: string
  verified: boolean
}

// Sample data - replace with real data from your API
const heroIdeas: HeroIdea[] = [
  {
    id: "1",
    title: "BioTech Innovations",
    creator: "Dr. Sarah Chen",
    description: "Revolutionary gene editing and biotech concepts transforming medicine and longevity research",
    floorPrice: 3.62,
    totalVolume: 358.4,
    items: 9999,
    listed: 4.4,
    change: -11.7,
    image: "/api/placeholder/400/400",
    icon: Dna,
    category: "Biotechnology",
    verified: true
  },
  {
    id: "2",
    title: "Quantum Computing Systems",
    creator: "Dr. Michael Torres",
    description: "Next-generation quantum algorithms and computing architectures for solving complex problems",
    floorPrice: 12.79,
    totalVolume: 892.1,
    items: 3200,
    listed: 8.2,
    change: 15.3,
    image: "/api/placeholder/400/400",
    icon: Atom,
    category: "Quantum Computing",
    verified: true
  },
  {
    id: "3",
    title: "AI Neural Networks",
    creator: "Neural Labs Collective",
    description: "Advanced artificial intelligence systems and neural network innovations",
    floorPrice: 11.59,
    totalVolume: 1250.8,
    items: 5000,
    listed: 6.1,
    change: 12.4,
    image: "/api/placeholder/400/400",
    icon: Brain,
    category: "Artificial Intelligence",
    verified: true
  },
  {
    id: "4", 
    title: "Climate Solutions Hub",
    creator: "EcoTech Collective",
    description: "Sustainable technology and green innovations for environmental challenges",
    floorPrice: 47.40,
    totalVolume: 2100.5,
    items: 12500,
    listed: 3.8,
    change: 48.7,
    image: "/api/placeholder/400/400",
    icon: Leaf,
    category: "Environmental Tech",
    verified: true
  },
  {
    id: "5",
    title: "Space Exploration Tech",
    creator: "Cosmos Innovations",
    description: "Aerospace engineering and space exploration breakthrough concepts",
    floorPrice: 0.03,
    totalVolume: 675.2,
    items: 1800,
    listed: 12.5,
    change: -8.2,
    image: "/api/placeholder/400/400",
    icon: Rocket,
    category: "Aerospace",
    verified: true
  }
]

const creatorProfiles: CreatorProfile[] = [
  {
    id: "1",
    name: "BioTech Innovations",
    avatar: "/api/placeholder/50/50",
    totalIdeas: 9999,
    floorPrice: 3.62,
    change: 11.7,
    verified: true,
    rank: 1
  },
  {
    id: "2",
    name: "Quantum Research Labs", 
    avatar: "/api/placeholder/50/50",
    totalIdeas: 3200,
    floorPrice: 12.79,
    change: 0.8,
    verified: true,
    rank: 2
  },
  {
    id: "3",
    name: "Neural AI Collective",
    avatar: "/api/placeholder/50/50", 
    totalIdeas: 5000,
    floorPrice: 11.59,
    change: 1.7,
    verified: true,
    rank: 3
  },
  {
    id: "4",
    name: "Climate Tech Solutions",
    avatar: "/api/placeholder/50/50",
    totalIdeas: 12500,
    floorPrice: 47.40,
    change: -0.2,
    verified: true,
    rank: 4
  },
  {
    id: "5",
    name: "Space Innovation Labs",
    avatar: "/api/placeholder/50/50",
    totalIdeas: 1800,
    floorPrice: 0.03,
    change: 0,
    verified: true,
    rank: 5
  },
  {
    id: "6",
    name: "Fusion Energy Research",
    avatar: "/api/placeholder/50/50",
    totalIdeas: 2400,
    floorPrice: 1.48,
    change: 4.5,
    verified: true,
    rank: 6
  }
]

const featuredCollections: IdeaCollection[] = [
  {
    id: "1",
    name: "BioTech Innovations",
    creator: "Dr. Sarah Chen",
    description: "Revolutionary gene editing and biotech concepts",
    floorPrice: 0.0309,
    volume: 358.4,
    change: -17.7,
    itemCount: 9999,
    image: "/api/placeholder/400/400",
    verified: true
  },
  {
    id: "2", 
    name: "AI Future Systems",
    creator: "Neural Labs",
    description: "Next-gen artificial intelligence architectures",
    floorPrice: 0.002,
    volume: 750.2,
    change: 15.3,
    itemCount: 5000,
    image: "/api/placeholder/400/400",
    verified: true
  },
  {
    id: "3",
    name: "Climate Solutions",
    creator: "EcoTech Collective",
    description: "Sustainable technology and green innovations",
    floorPrice: 0.0062,
    volume: 1250.8,
    change: 48.7,
    itemCount: 12500,
    image: "/api/placeholder/400/400",
    verified: true
  },
  {
    id: "4",
    name: "Space Tech Concepts",
    creator: "Cosmos Innovations",
    description: "Aerospace and space exploration ideas",
    floorPrice: 0.015,
    volume: 892.1,
    change: 12.4,
    itemCount: 3200,
    image: "/api/placeholder/400/400", 
    verified: true
  }
]

const trendingIdeas: TrendingIdea[] = [
  {
    id: "1",
    name: "Quantum Neural Network",
    symbol: "QNN",
    price: 89.95,
    change: -38.4,
    volume: 1250000,
    image: "/api/placeholder/80/80"
  },
  {
    id: "2",
    name: "CRISPR Longevity Protocol",
    symbol: "CLP",
    price: 9.32,
    change: 58.1,
    volume: 890000,
    image: "/api/placeholder/80/80"
  },
  {
    id: "3",
    name: "Fusion Energy Cell",
    symbol: "FEC", 
    price: 1.78,
    change: 36.8,
    volume: 2100000,
    image: "/api/placeholder/80/80"
  },
  {
    id: "4",
    name: "Neural Interface",
    symbol: "NIN",
    price: 89.92,
    change: 32.9,
    volume: 675000,
    image: "/api/placeholder/80/80"
  },
  {
    id: "5",
    name: "Bioprinter 3D",
    symbol: "B3D",
    price: 1.70,
    change: 30.4,
    volume: 1800000,
    image: "/api/placeholder/80/80"
  },
  {
    id: "6",
    name: "Synthetic Biology",
    symbol: "SYN",
    price: 89.92,
    change: 21.4,
    volume: 920000,
    image: "/api/placeholder/80/80"
  }
]

const featuredDrops: FeaturedDrop[] = [
  {
    id: "1",
    title: "Quantum Computing Breakthrough",
    creator: "Quantum Labs",
    description: "Revolutionary quantum algorithm concepts",
    launchDate: "August 21 at 6:00 AM PDT",
    image: "/api/placeholder/400/300",
    category: "Technology"
  },
  {
    id: "2", 
    title: "The Gene Collection",
    creator: "BioGenesis",
    description: "Next-generation CRISPR applications",
    launchDate: "August 21 at 10:00 AM PDT", 
    image: "/api/placeholder/400/300",
    category: "Biotechnology"
  },
  {
    id: "3",
    title: "Solar Revolution",
    creator: "SolarTech Inc",
    description: "Advanced photovoltaic innovations",
    launchDate: "August 26 at 12:00 PM PDT",
    image: "/api/placeholder/400/300", 
    category: "Energy"
  },
  {
    id: "4",
    title: "Mars Colony Blueprint", 
    creator: "Space Pioneers",
    description: "Comprehensive Mars settlement concepts",
    launchDate: "September 1 at 2:00 PM PDT",
    image: "/api/placeholder/400/300",
    category: "Aerospace"
  }
]

const topMovers: TopMover[] = [
  {
    id: "1",
    title: "Regenerative Medicine",
    creator: "BioRegen Labs",
    floorPrice: 0.449,
    change: 173.1,
    volume: 2500000,
    image: "/api/placeholder/300/300"
  },
  {
    id: "2",
    title: "Crypto Algorithms",
    creator: "CryptoDev",
    floorPrice: 0.98,
    change: 102.8,
    volume: 1800000,
    image: "/api/placeholder/300/300"
  },
  {
    id: "3",
    title: "AI Consciousness",
    creator: "Mind Tech",
    floorPrice: 0.005,
    change: 88.5,
    volume: 3200000,
    image: "/api/placeholder/300/300"
  }
]

const categories = [
  { name: "All", icon: Grid3X3, count: 0 },
  { name: "Biotechnology", icon: Dna, count: 12500 },
  { name: "AI & Machine Learning", icon: Brain, count: 8900 },
  { name: "Energy & Climate", icon: Zap, count: 6750 },
  { name: "Space Technology", icon: Rocket, count: 4200 },
  { name: "Quantum Computing", icon: Atom, count: 2800 },
  { name: "Healthcare", icon: Heart, count: 15600 },
  { name: "Finance & Crypto", icon: DollarSign, count: 9200 },
  { name: "Transportation", icon: Car, count: 5400 },
  { name: "Agriculture", icon: Leaf, count: 3600 },
  { name: "Manufacturing", icon: Building, count: 7800 }
]

export default function ExplorePage() {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [timeRange, setTimeRange] = useState("1d")
  const [currentHeroIndex, setCurrentHeroIndex] = useState(0)

  // Auto-rotate carousel every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentHeroIndex((prev) => (prev + 1) % heroIdeas.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  const nextHero = () => {
    setCurrentHeroIndex((prev) => (prev + 1) % heroIdeas.length)
  }

  const prevHero = () => {
    setCurrentHeroIndex((prev) => (prev - 1 + heroIdeas.length) % heroIdeas.length)
  }

  const currentIdea = heroIdeas[currentHeroIndex]
  const IconComponent = currentIdea.icon

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
      {/* Header with search */}
      <div className="relative px-4 pt-20 pb-8">
        <div className="max-w-7xl mx-auto">
          {/* Search and filters bar */}
          <div className="flex flex-col lg:flex-row items-center justify-between mb-8 gap-4">
            <div className="flex items-center space-x-4 w-full lg:w-auto">
              <div className="relative flex-1 lg:w-96">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search ideas across 75+ sectors"
                  className="w-full bg-slate-800/80 backdrop-blur border border-slate-700 rounded-xl pl-10 pr-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                />
              </div>
              <Button variant="outline" size="sm" className="border-slate-700 text-slate-300">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className={viewMode === "grid" ? "bg-cyan-600" : "border-slate-700 text-slate-300"}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
                className={viewMode === "list" ? "bg-cyan-600" : "border-slate-700 text-slate-300"}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section with Carousel + Sidebar */}
      <div className="relative">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex gap-6">
            {/* Left: Hero Carousel */}
            <div className="flex-1">
              <div className="relative bg-gradient-to-r from-slate-900/90 to-blue-900/90 backdrop-blur-xl rounded-3xl border border-slate-700/50 overflow-hidden">
                <div className="absolute inset-0 bg-[url('/api/placeholder/1200/600')] bg-cover bg-center opacity-30" />
                
                {/* Carousel navigation */}
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={prevHero}
                    className="bg-slate-900/80 border-slate-700 text-white hover:bg-slate-800 rounded-full p-2"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </div>
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={nextHero}
                    className="bg-slate-900/80 border-slate-700 text-white hover:bg-slate-800 rounded-full p-2"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>

                {/* Carousel dots */}
                <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
                  {heroIdeas.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentHeroIndex(index)}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index === currentHeroIndex ? 'bg-cyan-400' : 'bg-slate-600'
                      }`}
                    />
                  ))}
                </div>
                
                <div className="relative p-8 lg:p-12">
                  <div className="flex flex-col lg:flex-row items-center gap-8">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-4">
                        <Badge className="bg-cyan-500/20 text-cyan-300 border-cyan-500/30">
                          <Sparkles className="h-3 w-3 mr-1" />
                          Featured Collection
                        </Badge>
                        {currentIdea.verified && (
                          <Badge className="bg-green-500/20 text-green-300 border-green-500/30">
                            Verified
                          </Badge>
                        )}
                      </div>
                      <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4">
                        {currentIdea.title}
                      </h1>
                      <p className="text-xl text-slate-300 mb-6 max-w-2xl">
                        By {currentIdea.creator} • {currentIdea.description}
                      </p>
                      
                      <div className="flex flex-wrap items-center gap-6 mb-8">
                        <div>
                          <p className="text-sm text-slate-400">FLOOR PRICE</p>
                          <p className="text-2xl font-bold text-white">{currentIdea.floorPrice} FRYE</p>
                          <p className={`text-sm ${currentIdea.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {currentIdea.change >= 0 ? '+' : ''}{currentIdea.change}%
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-slate-400">TOTAL VOLUME</p>
                          <p className="text-2xl font-bold text-white">{currentIdea.totalVolume}K FRYE</p>
                        </div>
                        <div>
                          <p className="text-sm text-slate-400">ITEMS</p>
                          <p className="text-2xl font-bold text-white">{currentIdea.items.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-sm text-slate-400">LISTED</p>
                          <p className="text-2xl font-bold text-white">{currentIdea.listed}%</p>
                        </div>
                      </div>

                      <div className="flex gap-4">
                        <Button className="bg-cyan-600 hover:bg-cyan-700 text-white px-8">
                          Explore Collection
                        </Button>
                        <Button variant="outline" className="border-slate-600 text-slate-300">
                          View Details
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex gap-4">
                      <div className="w-32 h-32 rounded-2xl bg-gradient-to-br from-cyan-500 to-purple-600 p-1">
                        <div className="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center">
                          <IconComponent className="h-16 w-16 text-cyan-400" />
                        </div>
                      </div>
                      <div className="w-32 h-32 rounded-2xl bg-gradient-to-br from-purple-500 to-pink-600 p-1">
                        <div className="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center">
                          <Microscope className="h-16 w-16 text-purple-400" />
                        </div>
                      </div>
                      <div className="w-32 h-32 rounded-2xl bg-gradient-to-br from-green-500 to-cyan-600 p-1">
                        <div className="w-full h-full rounded-xl bg-slate-800 flex items-center justify-center">
                          <Heart className="h-16 w-16 text-green-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right: Collections Sidebar */}
            <div className="w-80">
              <Card className="bg-slate-900/70 backdrop-blur border border-slate-700/50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-white">COLLECTION</h3>
                    <h3 className="text-lg font-semibold text-white">FLOOR</h3>
                  </div>
                  
                  <div className="space-y-4">
                    {creatorProfiles.map((profile, index) => (
                      <div key={profile.id} className="flex items-center justify-between group hover:bg-slate-800/30 p-2 rounded-lg transition-colors">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-cyan-500 to-purple-600 p-0.5">
                            <div className="w-full h-full rounded-full bg-slate-800 flex items-center justify-center">
                              {index === 0 && <Dna className="h-6 w-6 text-cyan-400" />}
                              {index === 1 && <Atom className="h-6 w-6 text-purple-400" />}
                              {index === 2 && <Brain className="h-6 w-6 text-green-400" />}
                              {index === 3 && <Leaf className="h-6 w-6 text-emerald-400" />}
                              {index === 4 && <Rocket className="h-6 w-6 text-orange-400" />}
                              {index === 5 && <Zap className="h-6 w-6 text-yellow-400" />}
                            </div>
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-white text-sm group-hover:text-cyan-300 transition-colors">
                                {profile.name}
                              </h4>
                              {profile.verified && (
                                <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
                                  ✓
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-slate-400">
                              {profile.totalIdeas.toLocaleString()} ideas
                            </p>
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <p className="font-semibold text-white text-sm">
                            {profile.floorPrice} FRYE
                          </p>
                          <div className={`flex items-center gap-1 text-xs ${
                            profile.change >= 0 ? 'text-green-400' : 'text-red-400'
                          }`}>
                            {profile.change >= 0 ? (
                              <TrendingUp className="h-3 w-3" />
                            ) : (
                              <TrendingDown className="h-3 w-3" />
                            )}
                            <span>
                              {profile.change >= 0 ? '+' : ''}{profile.change}%
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 pt-4 border-t border-slate-700">
                    <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-800">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      View All Collections
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="max-w-7xl mx-auto px-4 pt-12 pb-12">
        {/* Top Collections */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">Top Collections</h2>
          <div className="flex items-center gap-4">
            <p className="text-slate-400 text-sm">Innovation Floor Price change in the past day</p>
            <div className="flex border border-slate-700 rounded-lg">
              {["1d", "7d", "30d"].map((range) => (
                <Button
                  key={range}
                  variant={timeRange === range ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setTimeRange(range)}
                  className={`rounded-none first:rounded-l-lg last:rounded-r-lg ${
                    timeRange === range 
                      ? "bg-cyan-600 text-white" 
                      : "text-slate-400 hover:text-white"
                  }`}
                >
                  {range}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Collections ranking list */}
        <div className="grid gap-4 mb-12">
          {featuredCollections.map((collection, index) => (
            <Card key={collection.id} className="bg-slate-900/70 backdrop-blur border border-slate-700/50 hover:border-slate-600 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-center gap-6">
                  <div className="text-2xl font-bold text-slate-400 w-8 text-center">
                    {index + 1}
                  </div>
                  
                  <div className="flex items-center gap-4 flex-1">
                    <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-cyan-500 to-purple-600 p-0.5">
                      <div className="w-full h-full rounded-lg bg-slate-800 flex items-center justify-center">
                        {index === 0 && <Dna className="h-8 w-8 text-cyan-400" />}
                        {index === 1 && <Brain className="h-8 w-8 text-purple-400" />}
                        {index === 2 && <Leaf className="h-8 w-8 text-green-400" />}
                        {index === 3 && <Rocket className="h-8 w-8 text-orange-400" />}
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold text-white">{collection.name}</h3>
                        {collection.verified && (
                          <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
                            ✓
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-slate-400">Floor price: {collection.floorPrice.toFixed(4)} FRYE —</p>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center gap-2 justify-end mb-1">
                      <span className="text-lg font-bold text-white">
                        {collection.floorPrice.toFixed(2)} FRYE
                      </span>
                      <div className={`flex items-center gap-1 ${
                        collection.change >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {collection.change >= 0 ? (
                          <ArrowUpRight className="h-4 w-4" />
                        ) : (
                          <ArrowDownRight className="h-4 w-4" />
                        )}
                        <span className="text-sm font-medium">
                          {Math.abs(collection.change).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-slate-400">
                      {collection.volume.toFixed(1)}K FRYE volume
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Trending Ideas */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-white mb-2">Trending Ideas</h2>
          <p className="text-slate-400 mb-6">Largest price change in the past hour</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {trendingIdeas.map((idea) => (
              <Card key={idea.id} className="bg-slate-900/70 backdrop-blur border border-slate-700/50 hover:border-slate-600 transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-cyan-500 to-purple-600 p-0.5">
                      <div className="w-full h-full rounded-lg bg-slate-800 flex items-center justify-center">
                        <Lightbulb className="h-6 w-6 text-cyan-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-white">{idea.name}</h3>
                        <Badge className="bg-slate-700 text-slate-300 text-xs">{idea.symbol}</Badge>
                      </div>
                      <p className="text-sm text-slate-400">${idea.price.toFixed(2)} • {idea.volume.toLocaleString()} vol</p>
                    </div>
                    
                    <div className={`flex items-center gap-1 ${
                      idea.change >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {idea.change >= 0 ? (
                        <TrendingUp className="h-4 w-4" />
                      ) : (
                        <TrendingDown className="h-4 w-4" />
                      )}
                      <span className="font-semibold">
                        {idea.change >= 0 ? '+' : ''}{idea.change.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Featured Drops */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-white mb-2">Featured Drops</h2>
          <p className="text-slate-400 mb-6">This week's curated live and upcoming drops</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredDrops.map((drop) => (
              <Card key={drop.id} className="bg-slate-900/70 backdrop-blur border border-slate-700/50 hover:border-slate-600 transition-all duration-300 group overflow-hidden">
                <div className="relative h-48 bg-gradient-to-br from-cyan-500/20 to-purple-600/20">
                  <div className="absolute inset-0 flex items-center justify-center">
                    {drop.category === "Technology" && <Cpu className="h-16 w-16 text-cyan-400" />}
                    {drop.category === "Biotechnology" && <Dna className="h-16 w-16 text-purple-400" />}
                    {drop.category === "Energy" && <Zap className="h-16 w-16 text-yellow-400" />}
                    {drop.category === "Aerospace" && <Rocket className="h-16 w-16 text-orange-400" />}
                  </div>
                  <Badge className="absolute top-3 left-3 bg-slate-800/80 text-slate-300">
                    {drop.category}
                  </Badge>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-white mb-2 group-hover:text-cyan-300 transition-colors">
                    {drop.title}
                  </h3>
                  <p className="text-sm text-slate-400 mb-3">{drop.creator}</p>
                  <div className="flex items-center gap-2 text-xs text-slate-500">
                    <Calendar className="h-3 w-3" />
                    {drop.launchDate}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Top Movers Today */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-white mb-2">Top Movers Today</h2>
          <p className="text-slate-400 mb-6">Largest floor price change in the past day</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {topMovers.map((mover) => (
              <Card key={mover.id} className="bg-slate-900/70 backdrop-blur border border-slate-700/50 hover:border-slate-600 transition-all duration-300 group overflow-hidden">
                <div className="relative h-40 bg-gradient-to-br from-green-500/20 to-emerald-600/20">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <TrendingUp className="h-12 w-12 text-green-400" />
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-white mb-1 group-hover:text-green-300 transition-colors">
                    {mover.title}
                  </h3>
                  <p className="text-sm text-slate-400 mb-3">{mover.creator}</p>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-slate-400">Floor price</p>
                      <p className="font-bold text-white">{mover.floorPrice.toFixed(3)} FRYE</p>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-green-400">
                        <TrendingUp className="h-4 w-4" />
                        <span className="font-bold">+{mover.change.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Trending Collections by Sector */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-white mb-2">Trending Collections</h2>
          <p className="text-slate-400 mb-6">Highest sales in the past hour</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: "Quantum Computing", change: 3.4, eth: "0.09 APE", icon: Atom, color: "from-purple-500 to-indigo-600" },
              { name: "Gene Therapy", change: -5.7, eth: "< 0.01 FRYE", icon: Dna, color: "from-green-500 to-emerald-600" },
              { name: "Space Mining", change: -8.7, eth: "0.09 FRYE", icon: Rocket, color: "from-orange-500 to-red-600" },
              { name: "Neural Interfaces", change: -8.4, eth: "0.89 FRYE", icon: Brain, color: "from-cyan-500 to-blue-600" }
            ].map((collection, index) => (
              <Card key={index} className="bg-slate-900/70 backdrop-blur border border-slate-700/50 hover:border-slate-600 transition-all duration-300 group overflow-hidden">
                <div className={`relative h-32 bg-gradient-to-br ${collection.color}/20`}>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <collection.icon className="h-12 w-12 text-white" />
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-white mb-2 group-hover:text-cyan-300 transition-colors">
                    {collection.name}
                  </h3>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-slate-400">{collection.eth}</p>
                    <div className={`flex items-center gap-1 ${
                      collection.change >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {collection.change >= 0 ? (
                        <TrendingUp className="h-3 w-3" />
                      ) : (
                        <TrendingDown className="h-3 w-3" />
                      )}
                      <span className="text-sm font-medium">
                        {collection.change >= 0 ? '+' : ''}{collection.change}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Category Filter Tabs */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.name}
                variant={selectedCategory === category.name ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.name)}
                className={`${
                  selectedCategory === category.name
                    ? "bg-cyan-600 text-white"
                    : "border-slate-700 text-slate-300 hover:border-slate-600"
                }`}
              >
                <category.icon className="h-4 w-4 mr-2" />
                {category.name}
                {category.count > 0 && (
                  <Badge className="ml-2 bg-slate-600 text-xs">
                    {category.count.toLocaleString()}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* Explore Collections Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {featuredCollections.map((collection) => (
            <Card key={collection.id} className="bg-slate-900/70 backdrop-blur border border-slate-700/50 hover:border-slate-600 transition-all duration-300 group overflow-hidden">
              <div className="relative h-48 bg-gradient-to-br from-cyan-500/20 to-purple-600/20">
                <div className="absolute inset-0 flex items-center justify-center">
                  <Lightbulb className="h-16 w-16 text-cyan-400" />
                </div>
                {collection.verified && (
                  <Badge className="absolute top-3 right-3 bg-blue-500/20 text-blue-300 border-blue-500/30">
                    ✓
                  </Badge>
                )}
              </div>
              <CardContent className="p-4">
                <h3 className="font-semibold text-white mb-1 group-hover:text-cyan-300 transition-colors">
                  {collection.name}
                </h3>
                <p className="text-sm text-slate-400 mb-3">{collection.creator}</p>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-slate-500">Floor</p>
                    <p className="font-bold text-white">{collection.floorPrice.toFixed(4)} FRYE</p>
                  </div>
                  <div className="text-right">
                    <div className={`flex items-center gap-1 ${
                      collection.change >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {collection.change >= 0 ? (
                        <TrendingUp className="h-3 w-3" />
                      ) : (
                        <TrendingDown className="h-3 w-3" />
                      )}
                      <span className="text-xs font-medium">
                        {collection.change >= 0 ? '+' : ''}{collection.change.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}