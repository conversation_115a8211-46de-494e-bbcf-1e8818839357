{"name": "frye-ecosystem", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.400.0", "next": "^14.2.31", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "^2.12.7", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "eslint": "^9.7.0", "eslint-config-next": "14.2.5", "postcss": "^8.4.39", "tailwindcss": "^3.4.6", "typescript": "^5.5.3"}}